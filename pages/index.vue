<template>
  <div class="home-container">
    <h1>首页</h1>

    <div class="debug-info">
      <h2>调试信息</h2>
      <p>ArticleList组件导入状态: {{ ArticleList ? '✅ 成功' : '❌ 失败' }}</p>
      <p>当前数据: {{ JSON.stringify(articleValue, null, 2) }}</p>
    </div>

    <div class="article-section">
      <h2>ArticleList组件</h2>
      <ClientOnly>
        <ArticleList
          v-model="articleValue"
          @update:modelValue="handleChange"
        />
        <template #fallback>
          <div class="loading">ArticleList组件加载中...</div>
        </template>
      </ClientOnly>
    </div>
  </div>
</template>

<script setup>
// 直接导入组件进行测试
import { ArticleList } from "officialblock";
import 'officialblock/style.css'

// 页面元数据
// definePageMeta({
//   layout: "default",
// });
const articleValue = ref({
  background: 'white',
  isPreview: false,
  data: [
    {
      id: '1',
      type: 'Article',
      data: {
        title: '测试文章标题',
        content: '这是一个测试文章的内容...',
        author: '测试作者',
        date: '2024-01-01',
        buttonList: [],
        linkList: []
      }
    }
  ]
});

const handleChange = (value) => {
  console.log('ArticleList数据变化:', value);
  articleValue.value = value;
};
</script>

<style scoped>
.home-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #fff;
}

.debug-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f0f8ff;
  border: 1px solid #b0d4f1;
  border-radius: 4px;
}

.debug-info h2 {
  margin-bottom: 10px;
  color: #1890ff;
}

.debug-info p {
  margin: 5px 0;
  font-family: monospace;
  font-size: 12px;
}

.article-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f9f9f9;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
}

.loading {
  padding: 20px;
  text-align: center;
  color: #1890ff;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
}
</style>
