<template>
  <div class="debug-page">
    <h1>组件调试页面</h1>
    
    <div class="debug-section">
      <h2>1. 基础Arco Design组件测试</h2>
      <a-button type="primary">Arco按钮测试</a-button>
      <a-input placeholder="Arco输入框测试" style="margin-left: 10px; width: 200px;" />
    </div>
    
    <div class="debug-section">
      <h2>2. ArticleList组件测试</h2>
      <div class="component-status">
        <p>组件注册状态: {{ componentStatus }}</p>
        <p>全局组件列表: {{ globalComponents }}</p>
      </div>
      
      <div class="article-test">
        <h3>ArticleList组件:</h3>
        <ClientOnly>
          <ArticleList 
            v-if="showArticleList"
            v-model="articleData"
          />
          <div v-else class="error-message">
            ArticleList组件未找到或未正确注册
          </div>
          <template #fallback>
            <div class="loading">ArticleList加载中...</div>
          </template>
        </ClientOnly>
      </div>
    </div>
    
    <div class="debug-section">
      <h2>3. 调试信息</h2>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'

const showArticleList = ref(false)
const componentStatus = ref('检查中...')
const globalComponents = ref([])
const debugInfo = ref({})

const articleData = ref({
  background: 'white',
  isPreview: false,
  data: [
    {
      id: '1',
      type: 'Article',
      data: {
        title: '测试文章',
        content: '这是一个测试文章的内容',
        buttonList: [],
        linkList: []
      }
    }
  ]
})

onMounted(() => {
  console.log('调试页面已挂载')
  
  // 检查组件注册状态
  const instance = getCurrentInstance()
  if (instance) {
    const app = instance.appContext.app
    const components = app._context.components || {}
    
    globalComponents.value = Object.keys(components)
    console.log('全局组件:', globalComponents.value)
    
    // 检查ArticleList是否已注册
    if (components.ArticleList) {
      componentStatus.value = '✅ ArticleList已注册'
      showArticleList.value = true
      console.log('✅ ArticleList组件已找到')
    } else {
      componentStatus.value = '❌ ArticleList未注册'
      console.log('❌ ArticleList组件未找到')
    }
    
    debugInfo.value = {
      hasArticleList: !!components.ArticleList,
      componentCount: Object.keys(components).length,
      components: globalComponents.value
    }
  }
})
</script>

<style scoped>
.debug-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  background: #f9f9f9;
}

.component-status {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.article-test {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
}

.error-message {
  color: #ff4d4f;
  padding: 20px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
}

.loading {
  color: #1890ff;
  padding: 20px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
}

pre {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
