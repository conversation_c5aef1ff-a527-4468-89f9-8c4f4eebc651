// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  modules: ['arco-design-nuxt-module', 'dayjs-nuxt'],
  
  // 插件配置
  plugins: [
    { src: '~/plugins/official-block.client.js', mode: 'client' },
    { src: '~/plugins/test.client.js', mode: 'client' }
  ],
  css: [
    '@/assets/css/variables.css',
    '@arco-design/web-vue/dist/arco.css',
    'officialblock/style.css'
  ],
  // 构建配置
  build: {
    transpile: ['officialblock', '@arco-design/web-vue']
  },

  ssr: true,

  // SSR性能优化配置
  nitro: {
    // 启用压缩
    compressPublicAssets: {
      gzip: true,
      brotli: true
    },
  },

  // 应用级配置
  app: {
    // 页面过渡效果
    pageTransition: { name: 'page', mode: 'out-in' },
  },
   vite: {
    css: {
      preprocessorOptions: {
        scss: {
          // additionalData: '@use "@/assets/scss/_vars.scss" as *;',
        },
      },
    },
    plugins: [
      require('vite-plugin-devtools-json').default()
    ],
    // 优化外部依赖
    optimizeDeps: {
      include: ['@arco-design/web-vue', 'officialblock']
    },
    // 解决SSR环境下的全局对象问题
    define: {
      global: 'globalThis',
    },
    // 构建配置
    build: {
      rollupOptions: {
        external: [],
        output: {
          globals: {}
        }
      }
    }
  },
})