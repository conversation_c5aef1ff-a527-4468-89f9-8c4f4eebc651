export default defineNuxtPlugin({
  name: 'official-block',
  parallel: true,
  async setup(nuxtApp) {
    // 只在客户端执行
    if (import.meta.client) {
      console.log('🚀 OfficialBlock 插件开始执行...');

      try {
        // 动态导入 officialblock 组件
        console.log('📦 正在导入 officialblock 模块...');
        const OfficialBlockModule = await import('officialblock');

        // 检查导出的内容
        console.log('📋 OfficialBlock 模块内容:', OfficialBlockModule);
        console.log('🔑 OfficialBlock 模块键:', Object.keys(OfficialBlockModule));

        // 使用插件方式注册所有组件
        if (OfficialBlockModule.default && typeof OfficialBlockModule.default.install === 'function') {
          console.log('🔌 使用插件方式注册组件...');
          nuxtApp.vueApp.use(OfficialBlockModule.default);
          console.log('✅ OfficialBlock 插件已成功注册');
        } else {
          console.warn('⚠️ OfficialBlock 默认导出不是插件，使用手动注册方式');

          // 手动注册各个组件
          if (OfficialBlockModule.ArticleList) {
            nuxtApp.vueApp.component('ArticleList', OfficialBlockModule.ArticleList);
            console.log('✅ ArticleList 组件已注册');
          } else {
            console.error('❌ ArticleList 组件未找到');
          }

          if (OfficialBlockModule.HeroSlide) {
            nuxtApp.vueApp.component('HeroSlide', OfficialBlockModule.HeroSlide);
            console.log('✅ HeroSlide 组件已注册');
          }

          if (OfficialBlockModule.RichTextEditor) {
            nuxtApp.vueApp.component('RichTextEditor', OfficialBlockModule.RichTextEditor);
            console.log('✅ RichTextEditor 组件已注册');
          }

          if (OfficialBlockModule.ThemePreview) {
            nuxtApp.vueApp.component('ThemePreview', OfficialBlockModule.ThemePreview);
            console.log('✅ ThemePreview 组件已注册');
          }
        }

        // 验证组件是否已注册
        const registeredComponents = Object.keys(nuxtApp.vueApp._context.components || {});
        console.log('🎯 当前已注册的组件:', registeredComponents);

        console.log('✅ OfficialBlock 组件注册完成');

      } catch (error) {
        console.error('❌ OfficialBlock 组件注册失败:', error);
        console.error('❌ 错误详情:', error.stack);
      }
    } else {
      console.log('🔄 在服务端环境中，跳过组件注册');
    }
  }
})