// plugins/official-block.client.js
import { defineNuxtPlugin } from '#app';

export default defineNuxtPlugin(async (nuxtApp) => {
  // 确保只在客户端执行
  if (process.client) {
    try {
      console.log('开始注册 OfficialBlock 组件...');

      // 动态导入 officialblock 组件
      const OfficialBlockModule = await import('officialblock');

      // 检查导出的内容
      console.log('OfficialBlock 模块内容:', OfficialBlockModule);
      console.log('OfficialBlock 模块键:', Object.keys(OfficialBlockModule));

      // 使用插件方式注册所有组件
      if (OfficialBlockModule.default && typeof OfficialBlockModule.default.install === 'function') {
        nuxtApp.vueApp.use(OfficialBlockModule.default);
        console.log('✅ OfficialBlock 插件已成功注册');
      } else {
        console.warn('⚠️ OfficialBlock 默认导出不是插件');

        // 手动注册各个组件
        if (OfficialBlockModule.ArticleList) {
          nuxtApp.vueApp.component('ArticleList', OfficialBlockModule.ArticleList);
          console.log('✅ ArticleList 组件已注册');
        }

        if (OfficialBlockModule.HeroSlide) {
          nuxtApp.vueApp.component('HeroSlide', OfficialBlockModule.HeroSlide);
          console.log('✅ HeroSlide 组件已注册');
        }

        if (OfficialBlockModule.RichTextEditor) {
          nuxtApp.vueApp.component('RichTextEditor', OfficialBlockModule.RichTextEditor);
          console.log('✅ RichTextEditor 组件已注册');
        }

        if (OfficialBlockModule.ThemePreview) {
          nuxtApp.vueApp.component('ThemePreview', OfficialBlockModule.ThemePreview);
          console.log('✅ ThemePreview 组件已注册');
        }
      }

      console.log('✅ OfficialBlock 组件注册完成');

    } catch (error) {
      console.error('❌ OfficialBlock 组件注册失败:', error);
    }
  }
});